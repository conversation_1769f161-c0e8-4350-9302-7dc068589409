#!/bin/bash
set -e
sleep 15

# Force clean start
echo "==> Cleaning previous data..."
rm -rf ~/.ethermintd*

# 1. Initialize node
echo "==> Initializing ethermintd..."
cd ethermint
./init.sh
cd ../

# 2. Start node background
echo "==> Starting ethermintd..."
ethermintd start --metrics --pruning=nothing --evm.tracer=json --trace --log_level info --minimum-gas-prices=0.0001aphoton --json-rpc.api eth,txpool,personal,net,debug,web3,miner --api.enable &

ETHERMINT_PID=$!
echo "==> ethermintd PID: $ETHERMINT_PID"

# Wait for node to start
sleep 15

# Get account info automatically
echo "==> Getting account info..."
MAIN_PRIVATE_KEY=$(ethermintd keys unsafe-export-eth-key mykey --keyring-backend test)
MAIN_ETH_ADDRESS=$(ethermintd debug addr $(ethermintd keys show mykey -a --keyring-backend test) | grep "Address (EIP-55):" | awk '{print $3}')

echo "Main private key: $MAIN_PRIVATE_KEY"
echo "Main ETH address: $MAIN_ETH_ADDRESS"

# Deploy contract with auto-generated config
echo "==> Changing to erc20 directory..."
cd erc20

# Verify we're in the right directory
if [ ! -f "package.json" ]; then
  echo "❌ Error: package.json not found in erc20 directory"
  exit 1
fi

echo "==> Installing npm dependencies..."
npm install

# Create hardhat config with auto-detected private key
echo "==> Creating hardhat.config.js..."
cat > hardhat.config.js << 'EOF'
require("@nomicfoundation/hardhat-toolbox");
require("@nomicfoundation/hardhat-ignition-ethers");

module.exports = {
  solidity: "0.8.28",
  networks: {
    ethermint: {
      url: "http://localhost:8545",
      accounts: ["PRIVATE_KEY_PLACEHOLDER"],
      chainId: 9000,
      gas: 2100000,
      gasPrice: **********,
    }
  }
};
EOF

# Replace placeholder with actual private key
sed -i "s/PRIVATE_KEY_PLACEHOLDER/$MAIN_PRIVATE_KEY/g" hardhat.config.js

# Verify hardhat.config.js was created successfully
if [ -f "hardhat.config.js" ] && grep -q "$MAIN_PRIVATE_KEY" "hardhat.config.js"; then
  echo "==> hardhat.config.js created successfully"
else
  echo "❌ Error: Failed to create hardhat.config.js"
  exit 1
fi

# Update ignition module with auto-detected address
echo "==> Creating ignition module..."
cat > ignition/modules/MyERC20Token.js << 'EOF'
const { buildModule } = require("@nomicfoundation/hardhat-ignition/modules");

module.exports = buildModule("MyERC20TokenModule", (m) => {
  const tokenName = m.getParameter("tokenName", "DataCoinERC");
  const tokenSymbol = m.getParameter("tokenSymbol", "ERC");
  const initialSupply = m.getParameter("initialSupply", 1000000);
  const initialOwner = m.getParameter("initialOwner", "OWNER_ADDRESS_PLACEHOLDER");

  const token = m.contract("MyERC20Token", [
    tokenName,
    tokenSymbol,
    initialSupply,
    initialOwner
  ]);

  return { token };
});
EOF

# Replace placeholder with actual address
sed -i "s/OWNER_ADDRESS_PLACEHOLDER/$MAIN_ETH_ADDRESS/g" ignition/modules/MyERC20Token.js
echo "==> Ignition module created successfully"

echo "==> Deploying contract..."
yes | npx hardhat ignition deploy ./ignition/modules/MyERC20Token.js --network ethermint
sleep 20

# Lấy địa chỉ contract từ deployed_addresses.json
DEPLOYED_JSON="./ignition/deployments/chain-9000/deployed_addresses.json"
if [ -f "$DEPLOYED_JSON" ]; then
  CONTRACT_ADDRESS=$(jq -r '."MyERC20TokenModule#MyERC20Token"' "$DEPLOYED_JSON")
  echo "==> Contract deployed at: $CONTRACT_ADDRESS"

  # Ghi vào file .env.local của dapp - sử dụng đường dẫn tuyệt đối
  ENV_FILE="../dapp/.env.local"

  # Tạo backup của file .env.local hiện tại
  if [ -f "$ENV_FILE" ]; then
    cp "$ENV_FILE" "$ENV_FILE.backup"
    echo "==> Backed up existing .env.local"
  fi

  # Tạo nội dung mới cho .env.local
  cat > "$ENV_FILE" << EOF
# Contract Configuration
NEXT_PUBLIC_CONTRACT_ADDRESS=$CONTRACT_ADDRESS
NEXT_PUBLIC_CHAIN_ID=9000
NEXT_PUBLIC_RPC_URL=http://localhost:8545
NEXT_PUBLIC_COSMOS_REST_URL=http://localhost:1317

# API Configuration
NEXT_PUBLIC_API_BASE_URL=http://localhost:3001/api

# App Configuration
NEXT_PUBLIC_APP_NAME=DATACOIN dApp
NEXT_PUBLIC_TOKEN_NAME=DATACOIN ERC20
NEXT_PUBLIC_TOKEN_SYMBOL=ERC
NEXT_PUBLIC_NATIVE_TOKEN_NAME=DATACOIN
NEXT_PUBLIC_NATIVE_TOKEN_SYMBOL=DTC

# New Token Configuration
EOF

  echo "==> Successfully updated $ENV_FILE with contract address: $CONTRACT_ADDRESS"

  # Kiểm tra xem file đã được ghi thành công chưa
  if [ -f "$ENV_FILE" ] && grep -q "$CONTRACT_ADDRESS" "$ENV_FILE"; then
    echo "==> Verification: Contract address found in .env.local"
  else
    echo "❌ Warning: Failed to verify contract address in .env.local"
  fi
else
  echo "❌ Deployment file not found: $DEPLOYED_JSON"
  echo "==> Available files in ignition/deployments/:"
  ls -la ignition/deployments/ 2>/dev/null || echo "No deployments directory found"
fi
sleep 10

echo "==> Dừng ethermintd..."
kill "$ETHERMINT_PID"
wait "$ETHERMINT_PID" 2>/dev/null

echo "==> Đã dừng ethermintd"

