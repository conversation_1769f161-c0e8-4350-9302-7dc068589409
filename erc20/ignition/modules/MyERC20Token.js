const { buildModule } = require("@nomicfoundation/hardhat-ignition/modules");

module.exports = buildModule("MyERC20TokenModule", (m) => {
  const tokenName = m.getParameter("tokenName", "DataCoinERC");
  const tokenSymbol = m.getParameter("tokenSymbol", "ERC");
  const initialSupply = m.getParameter("initialSupply", 1000000);
  const initialOwner = m.getParameter("initialOwner", "******************************************");
  // ****************************************** is the wallet that receives the first token when initialized
  // ****************************************** -> ethermintd debug addr $(ethermintd keys show mykey -a --keyring-backend test)-> Address (EIP-55)
  const token = m.contract("MyERC20Token", [
    tokenName,
    tokenSymbol,
    initialSupply,
    initialOwner
  ]);

  return { token };
});