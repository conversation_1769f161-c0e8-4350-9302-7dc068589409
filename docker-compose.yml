version: '3.8'

services:
  ethermint:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: ethermintd
    volumes:
      - ethermint_data:/root/.ethermintd
    ports:
      - "26657:26657"
      - "26656:26656"
      - "8545:8545"
      - "8546:8546"
    command: ethermintd start --metrics --pruning=nothing --evm.tracer=json --trace --log_level info --minimum-gas-prices=0.0001aphoton --json-rpc.api eth,txpool,personal,net,debug,web3,miner --api.enable --json-rpc.address="0.0.0.0:8545" --json-rpc.ws-address="0.0.0.0:8546" --json-rpc.enable

  dapp:
    build:
      context: .
    container_name: dapp
    ports:
      - "3000:3000"
    volumes:
      - ./dapp:/app
    working_dir: /app
    command: sh -c "npm install && chmod +x node_modules/.bin/next && npm run build && npm start"

    depends_on:
      - ethermint
volumes:
  ethermint_data:

